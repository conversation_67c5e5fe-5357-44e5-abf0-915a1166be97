from datetime import datetime
from typing import Dict, Optional, Any
from sqlalchemy import create_engine, text

import os
import pandas as pd
import logging

logger = logging.getLogger(__name__)


class DataPreparator:
    """
    Handles loading, cleaning, and preparing data (including database connection, VIN mappings,
    processing repair data, building vehicle info, adding working-only batteries).
    """

    def __init__(self):
        self.today = datetime.now().date()
        self.db_engine = None

        # Data containers
        self.hv_repair_df = None
        self.working_vehicles_df = None
        self.working_unique_df = None
        self.daily_stats_df = None

        # Processing results
        self.daily_stats_by_vehicle = {}  # Pre-indexed by vehicle_id for fast lookup
        self.vin_to_vehicle_id = {}
        self.battery_vehicles = {}  # battery_id -> list of battery appearance dicts
        self.vehicle_info = {}  # vin -> vehicle info
        self.unique_batteries = set()
        self.unique_vehicles = set()
        self.vin_without_vehicle_id = set()

        # Partial stats (will be completed by other classes)
        self.stats = {
            "total_batteries": 0,
            "total_vehicles": 0,
            "working_only_vehicles": 0,
            "errors": [],
        }

    def _initialize_database_connection(self):
        """Initialize database connection for VIN mappings and activity validation."""
        host = os.getenv("DB_HOST", "localhost")
        port = os.getenv("DB_PORT", "6543")
        database = os.getenv("DB_NAME", "LeitwartenDB")
        user = os.getenv("DB_USER", "datadump")
        password = os.getenv("DB_PASSWORD", "pAUjuLftyHURa5Ra")
        db_connection_string = (
            f"postgresql://{user}:{password}@{host}:{port}/{database}"
        )
        try:
            self.db_engine = create_engine(db_connection_string)
            with self.db_engine.connect() as conn:
                conn.execute(text("SELECT 1"))
                logger.info("✅ Database connection established successfully")
        except Exception as e:
            logger.error(f"❌ Database connection failed: {e}")
            logger.error(
                "Pipeline requires PostgreSQL connection for activity validation"
            )
            logger.error(f"Connection string: {db_connection_string}")
            logger.error("Please ensure PostgreSQL server is running and accessible")
            raise ConnectionError(f"Required database connection failed: {e}")

    def _load_vin_mappings(self):
        """Load VIN to vehicle_id mapping from database."""
        if not self.db_engine:
            logger.warning(
                "No database connection - cannot load VIN to vehicle_id mapping"
            )
            raise ConnectionError("Database connection required for VIN mappings")

        try:
            mapping_query = """
            SELECT vin, vehicle_id
            FROM public.vehicles
            WHERE vin IS NOT NULL
            """

            mapping_df = pd.read_sql(mapping_query, self.db_engine)
            logger.info(
                f"Loaded VIN mapping for {len(mapping_df):,} vehicles from database"
            )

            # Build VIN to vehicle_id mapping
            for _, row in mapping_df.iterrows():
                vin = row["vin"]
                if pd.notna(vin):
                    self.vin_to_vehicle_id[vin] = row["vehicle_id"]

            logger.info(
                f"Built VIN to vehicle_id mapping for {len(self.vin_to_vehicle_id)} vehicles"
            )

        except Exception as e:
            logger.error(f"Failed to load VIN to vehicle_id mapping: {e}")
            logger.error(f"Error details: {type(e).__name__}: {str(e)}")
            raise

    def _clean_daily_stats(self):
        """Clean daily stats data by filtering out invalid records."""
        if self.daily_stats_df is None:
            logger.warning("No daily stats data to clean")
            return

        initial_count = len(self.daily_stats_df)
        logger.info(f"Starting daily stats cleaning with {initial_count:,} records")

        # 1. Filter records with non-empty required fields
        required_fields = [
            "date",
            "timestamp_start",
            "timestamp_end",
            "km_start",
            "km_end",
        ]
        before_null_filter = len(self.daily_stats_df)

        # Check for null/empty values in required fields
        self.daily_stats_df = self.daily_stats_df.dropna(subset=required_fields)

        after_null_filter = len(self.daily_stats_df)
        removed_null = before_null_filter - after_null_filter
        if removed_null > 0:
            logger.info(
                f"Removed {removed_null:,} records with missing required fields"
            )

        # 2. Filter timestamp validation: timestamp_end > timestamp_start > 0
        before_timestamp_filter = len(self.daily_stats_df)

        timestamp_valid = (self.daily_stats_df["timestamp_start"] > 0) & (
            self.daily_stats_df["timestamp_end"]
            > self.daily_stats_df["timestamp_start"]
        )
        self.daily_stats_df = self.daily_stats_df[timestamp_valid]

        after_timestamp_filter = len(self.daily_stats_df)
        removed_timestamp = before_timestamp_filter - after_timestamp_filter
        if removed_timestamp > 0:
            logger.info(
                f"Removed {removed_timestamp:,} records with invalid timestamps"
            )

        # 3. Filter km validation: km_end >= km_start > 0
        before_km_filter = len(self.daily_stats_df)

        km_valid = (self.daily_stats_df["km_start"] > 0) & (
            self.daily_stats_df["km_end"] >= self.daily_stats_df["km_start"]
        )
        self.daily_stats_df = self.daily_stats_df[km_valid]

        after_km_filter = len(self.daily_stats_df)
        removed_km = before_km_filter - after_km_filter
        if removed_km > 0:
            logger.info(f"Removed {removed_km:,} records with invalid km values")

        final_count = len(self.daily_stats_df)
        total_removed = initial_count - final_count

        if total_removed > 0:
            logger.info(f"Daily stats cleaning complete:")
            logger.info(f"  - Initial records: {initial_count:,}")
            logger.info(f"  - Final records: {final_count:,}")
            logger.info(
                f"  - Total removed: {total_removed:,} ({total_removed/initial_count*100:.1f}%)"
            )
        else:
            logger.info("Daily stats cleaning complete: All records were valid")

    def load_data(self):
        """Load all data files."""
        logger.info("Loading data files...")

        self._initialize_database_connection()

        # Load HV repair data
        logger.info("Loading HV repair data...")
        self.hv_repair_df = pd.read_csv("input/hv_repair_2025-06-02b.csv")
        logger.info(f"Loaded {len(self.hv_repair_df)} HV repair records")

        # Load working vehicles data
        logger.info("Loading working vehicles data and daily stats...")
        self.working_vehicles_df = pd.read_csv("input/working_matching_vehicles.csv")
        self.working_unique_df = pd.read_csv("input/working_unique_vehicles.csv")
        self.daily_stats_df = pd.read_csv(
            "input/daily_stats.csv",
            dtype={"vehicle_id": "int", "km_start": "float", "km_end": "float"},
            parse_dates=["date"],
        )

        logger.info(f"Loaded {len(self.working_vehicles_df)} matching vehicles")
        logger.info(f"Loaded {len(self.working_unique_df)} unique vehicles")
        logger.info(f"Loaded {len(self.daily_stats_df)} daily stats records")

        # Combine working vehicles
        self.working_vehicles_df = pd.concat(
            [self.working_vehicles_df, self.working_unique_df], ignore_index=True
        )
        logger.info(f"Total working vehicles: {len(self.working_vehicles_df)}")

        logger.info("Loading VIN to vehicle_id mapping from database...")
        self._load_vin_mappings()

        logger.info("Cleaning daily stats data before indexing...")
        self._clean_daily_stats()

        logger.info("Pre-indexing daily stats by vehicle_id for fast lookup...")
        for vehicle_id, group in self.daily_stats_df.groupby("vehicle_id"):
            # Sort by date for each vehicle
            vehicle_data = group.sort_values("date").copy()
            self.daily_stats_by_vehicle[vehicle_id] = vehicle_data
        logger.info(
            f"Pre-indexed daily stats for {len(self.daily_stats_by_vehicle):,} vehicles"
        )
        logger.info(
            "Memory optimization: clear dailystats dataframe as we now have indexed data"
        )
        self.daily_stats_df = None

    def clean_data(self):
        """Clean and prepare data for processing."""
        logger.info("Cleaning data...")

        # Clean HV repair data
        self.hv_repair_df["created"] = pd.to_datetime(
            self.hv_repair_df["created"], errors="coerce"
        )
        self.hv_repair_df["battery_changed"] = self.hv_repair_df[
            "battery_changed"
        ].replace("--", None)
        self.hv_repair_df["battery_changed"] = pd.to_datetime(
            self.hv_repair_df["battery_changed"], errors="coerce"
        )

        # Create effective date (battery_changed if available, otherwise created)
        self.hv_repair_df["effective_date"] = self.hv_repair_df[
            "battery_changed"
        ].fillna(self.hv_repair_df["created"])

        # Clean battery IDs
        for col in ["battery_id_old", "battery_id_new"]:
            self.hv_repair_df[col] = self.hv_repair_df[col].astype(str)
            self.hv_repair_df[col] = self.hv_repair_df[col].replace(
                ["nan", "", " ", "None"], None
            )

        # Clean working vehicles data
        self.working_vehicles_df["erstzulassung"] = pd.to_datetime(
            self.working_vehicles_df["erstzulassung"], errors="coerce"
        )

        for col in ["master", "slave"]:
            if col in self.working_vehicles_df.columns:
                self.working_vehicles_df[col] = self.working_vehicles_df[col].astype(
                    str
                )
                self.working_vehicles_df[col] = self.working_vehicles_df[col].replace(
                    ["nan", "", " ", "None"], None
                )

        # Filter valid records
        self.hv_repair_df = self.hv_repair_df.dropna(subset=["vin", "effective_date"])
        self.working_vehicles_df = self.working_vehicles_df.dropna(subset=["vin"])

        logger.info(
            f"After cleaning: {len(self.hv_repair_df)} repair records, {len(self.working_vehicles_df)} working vehicles"
        )

    def process_hv_repair_data(self):
        """Process HV repair data to track battery appearances."""
        logger.info("Processing HV repair data...")

        # Sort by effective date to process chronologically. First event = first appearance = earliest seen date of batteries (old or new)
        self.hv_repair_df = self.hv_repair_df.sort_values("effective_date").reset_index(
            drop=True
        )

        for idx, row in self.hv_repair_df.iterrows():
            vin = row["vin"]
            effective_date = row["effective_date"].date()
            old_battery = row["battery_id_old"]
            new_battery = row["battery_id_new"]
            event_id = idx  # Use DataFrame index as event ID

            # Process old battery appearance
            if old_battery and pd.notna(old_battery):
                self.unique_batteries.add(old_battery)
                if old_battery not in self.battery_vehicles:
                    self.battery_vehicles[old_battery] = []
                self.battery_vehicles[old_battery].append(
                    {
                        "vin": vin,
                        "date": effective_date,
                        "column": "old",
                        "event_id": event_id,
                        "row_data": row,
                    }
                )
                logger.debug(
                    f"Battery {old_battery} appeared as old in vehicle {vin} on {effective_date}"
                )

            # Process new battery appearance
            if new_battery and pd.notna(new_battery):
                self.unique_batteries.add(new_battery)
                if new_battery not in self.battery_vehicles:
                    self.battery_vehicles[new_battery] = []
                self.battery_vehicles[new_battery].append(
                    {
                        "vin": vin,
                        "date": effective_date,
                        "column": "new",
                        "event_id": event_id,
                        "row_data": row,
                    }
                )
                logger.debug(
                    f"Battery {new_battery} appeared as new in vehicle {vin} on {effective_date}"
                )

            self.unique_vehicles.add(vin)

        logger.info(f"Found {len(self.unique_batteries)} batteries from repair data")

    def _get_first_active_date_for_vin(self, vin: str) -> Optional[datetime.date]:
        """Get the first active date for a VIN from daily stats."""
        if vin not in self.vin_to_vehicle_id:
            self.vin_without_vehicle_id.add(vin)
            return None

        try:
            vehicle_id = self.vin_to_vehicle_id[vin]

            if vehicle_id not in self.daily_stats_by_vehicle:
                logger.info(
                    f"Cannot get first active date for {vin} and vehicle_id {vehicle_id} - no daily stats"
                )
                return None

            vehicle_data = self.daily_stats_by_vehicle[vehicle_id]

            if len(vehicle_data) > 0:
                first_date = vehicle_data["date"].min()
                if pd.notna(first_date):
                    return first_date.date()
            return None

        except Exception as e:
            logger.error(f"Error getting first active date for {vin}: {e}")
            return None

    def _get_last_active_date_for_vin(self, vin: str) -> Optional[datetime.date]:
        """Get the last active date for a VIN from daily stats."""
        if vin not in self.vin_to_vehicle_id:
            self.vin_without_vehicle_id.add(vin)
            return None

        try:
            vehicle_id = self.vin_to_vehicle_id[vin]
            if vehicle_id not in self.daily_stats_by_vehicle:
                logger.info(
                    f"Cannot get last active date for {vin} and vehicle_id {vehicle_id} - no daily stats"
                )
                return None

            vehicle_data = self.daily_stats_by_vehicle[vehicle_id]

            if len(vehicle_data) > 0:
                last_date = vehicle_data["date"].max()
                if pd.notna(last_date):
                    return last_date.date()
            return None

        except Exception as e:
            logger.error(f"Error getting last active date for {vin}: {e}")
            return None

    def build_vehicle_info(self):
        """Build comprehensive vehicle information from working vehicles data."""
        logger.info("Building vehicle information...")

        for _, row in self.working_vehicles_df.iterrows():
            vin = row["vin"]
            self.unique_vehicles.add(vin)
            first_active_date = self._get_first_active_date_for_vin(vin)
            self.vehicle_info[vin] = {
                "vin": vin,
                "erstzulassung": (
                    row.get("erstzulassung").date()
                    if pd.notna(row.get("erstzulassung"))
                    else first_active_date
                ),
                "first_active_date": first_active_date,
                "master_battery": row.get("master"),
                "slave_battery": row.get("slave"),
                "akz": row.get("akz"),
                "last_active_date": self._get_last_active_date_for_vin(vin),
            }

        # We also need to get vehicle info for vehicles that appear in repair data but not in working data
        for _, row in self.hv_repair_df.iterrows():
            vin = row["vin"]
            if vin not in self.vehicle_info:
                first_active_date = self._get_first_active_date_for_vin(vin)
                self.vehicle_info[vin] = {
                    "vin": vin,
                    "erstzulassung": first_active_date,
                    "first_active_date": first_active_date,
                    "master_battery": None,
                    "slave_battery": None,
                    "akz": None,
                    "last_active_date": self._get_last_active_date_for_vin(vin),
                }

        logger.info(f"Built info for {len(self.vehicle_info)} vehicles")

        # Count number of vehicles that have both master and slave batteries
        vehicles_with_both_batteries = 0
        for vehicle_info in self.vehicle_info.values():
            if vehicle_info["master_battery"] and vehicle_info["slave_battery"]:
                vehicles_with_both_batteries += 1
        logger.info(
            f"Dual-battery vehicles from working data: {vehicles_with_both_batteries}"
        )

    def add_vehicles_from_working_only_data(self):
        """Add batteries from vehicles that only appear in working data (no repair history)."""
        logger.info("Adding batteries from working-only vehicles...")

        repair_vins = set(self.hv_repair_df["vin"].unique())
        working_vins = set(self.vehicle_info.keys())

        # Add batteries from vehicles that only appear in working data
        working_only_vins = working_vins - repair_vins
        for vin in working_only_vins:
            self._add_working_only_batteries(vin)

        self.stats["working_only_vehicles"] = len(working_only_vins)
        logger.info(
            f"Added batteries from {len(working_only_vins)} working-only vehicles"
        )

    def _add_working_only_batteries(self, vin: str):
        """Add batteries from vehicles that only appear in working data."""
        vehicle_info = self.vehicle_info[vin]
        erstzulassung = vehicle_info["erstzulassung"]

        if pd.isna(erstzulassung):
            logger.warning(f"Vehicle {vin} has no erstzulassung date")
            return

        # Add batteries from working data
        for battery_field in ["master_battery", "slave_battery"]:
            battery_id = vehicle_info[battery_field]
            if battery_id and pd.notna(battery_id):
                self.unique_batteries.add(battery_id)
                if battery_id not in self.battery_vehicles:
                    self.battery_vehicles[battery_id] = []

                # Check for duplicates - don't add if battery already has events for this VIN
                if not any(e["vin"] == vin for e in self.battery_vehicles[battery_id]):
                    self.battery_vehicles[battery_id].append(
                        {
                            "vin": vin,
                            "date": erstzulassung,
                            "column": None,
                            "event_id": None,
                            "row_data": None,
                        }
                    )
                    logger.info(
                        f"Added working-only battery {battery_id} for VIN {vin}"
                    )
                else:
                    logger.info(
                        f"Skipped duplicate: Battery {battery_id} already has repair events for VIN {vin}"
                    )

    def load_and_prepare_data(self) -> Dict[str, Any]:
        """
        Main method to load and prepare all data.

        Returns:
            Dict containing all prepared data structures for use by other classes.
        """
        self.load_data()
        self.clean_data()
        self.process_hv_repair_data()
        self.build_vehicle_info()
        self.add_vehicles_from_working_only_data()

        # Update final stats
        self.stats["total_batteries"] = len(self.unique_batteries)
        self.stats["total_vehicles"] = len(self.unique_vehicles)

        return {
            "hv_repair_df": self.hv_repair_df,
            "working_vehicles_df": self.working_vehicles_df,
            "daily_stats_by_vehicle": self.daily_stats_by_vehicle,
            "vin_to_vehicle_id": self.vin_to_vehicle_id,
            "battery_vehicles": self.battery_vehicles,
            "vehicle_info": self.vehicle_info,
            "unique_batteries": self.unique_batteries,
            "unique_vehicles": self.unique_vehicles,
            "vin_without_vehicle_id": self.vin_without_vehicle_id,
            "stats": self.stats,
        }
