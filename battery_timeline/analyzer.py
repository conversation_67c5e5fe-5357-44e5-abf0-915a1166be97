from __future__ import annotations
from pathlib import Path
import logging
import pandas as pd
from datetime import datetime
from typing import Dict, List
from battery_timeline.preparator import DataPreparator
from battery_timeline.output_generator import (
    OutputGenerator,
    OutputBundle,
    LocalCSVWriter,
)

logger = logging.getLogger(__name__)


class BatteryTimelineAnalyzer:
    def __init__(
        self,
        timeline_path: Path,
        *,
        out_path: Path = Path("output/accure.csv"),
        stats_path: Path = Path("output/analyze_battery_timeline_statistics.txt"),
        detailed_path: Path = Path("output/battery_lifecycle_detailed.csv"),
        detailed_stats_path: Path | None = None,
    ) -> None:
        self.timeline_path = timeline_path
        self.type_path = "input/battery_type.csv"
        self.out_path = out_path
        self.stats_path = stats_path
        self.detailed_path = detailed_path
        self.detailed_stats_path = (
            detailed_stats_path or stats_path
        )  # Share stats file by default
        self.today = datetime.now().date()

        # Data from DataPreparator
        self.prepared_data: Dict = {}
        self.daily_stats_by_vehicle: Dict = {}
        self.vin_to_vehicle_id: Dict = {}

        # populated later
        self.timeline_df: pd.DataFrame | None = None
        self.battery_type_df: pd.DataFrame | None = None
        self.accure_data: List[Dict] = []
        self.detailed_data: List[Dict] = []  # New: for detailed lifecycle report
        self.battery_timelines = {}

        self.stats: Dict[str, int | list] = {
            "total_batteries": 0,
            "batteries_with_type": 0,
            "batteries_without_type": 0,
            "total_intervals": 0,  # New: for detailed report
            "intervals_with_km_stand": 0,  # New: for detailed report
            "errors": [],
        }

    # ─────────────── load / clean ──────────────────

    def load(self) -> None:
        logger.info("Loading timeline data and prepared data...")
        try:
            # Load timeline data from CSV file (analyzer-specific)
            logger.info(f"Loading timeline data from {self.timeline_path}")
            self.timeline_df = pd.read_csv(self.timeline_path)

            # Load battery type data
            logger.info(f"Loading battery type data from {self.type_path}")
            self.battery_type_df = pd.read_csv(self.type_path)

            # Use DataPreparator to get daily stats and VIN mappings
            logger.info("Loading prepared data using DataPreparator...")
            preparator = DataPreparator()
            self.prepared_data = preparator.load_and_prepare_data()

            # Extract the data we need for analysis
            self.daily_stats_by_vehicle = self.prepared_data["daily_stats_by_vehicle"]
            self.vin_to_vehicle_id = self.prepared_data["vin_to_vehicle_id"]

            logger.info(
                f"Loaded {len(self.daily_stats_by_vehicle)} vehicles with daily stats"
            )
            logger.info(
                f"Loaded {len(self.vin_to_vehicle_id)} VIN to vehicle ID mappings"
            )

        except FileNotFoundError as exc:
            raise FileNotFoundError(f"Missing input file: {exc.filename}") from exc
        except Exception as exc:
            raise RuntimeError(f"Failed to load data: {exc}") from exc

    def clean(self) -> None:
        if self.timeline_df is not None:
            # Convert battery_id to string for consistent handling
            self.timeline_df["battery_id"] = self.timeline_df["battery_id"].astype(str)

            initial_count = len(self.timeline_df)
            self.timeline_df = self.timeline_df.dropna(subset=["battery_id"])

            self.timeline_df = self.timeline_df[
                ~self.timeline_df["battery_id"].str.contains(
                    "kein Tausch", case=False, na=False
                )
            ]

            # Remove records where interval_start >= interval_end (invalid/zero-duration intervals)
            before_interval_check = len(self.timeline_df)

            # Convert to datetime for comparison
            start_dates = pd.to_datetime(
                self.timeline_df["interval_start"], errors="coerce"
            )
            end_dates = pd.to_datetime(
                self.timeline_df["interval_end"], errors="coerce"
            )

            valid_intervals = (
                (start_dates < end_dates) & start_dates.notna() & end_dates.notna()
            )

            self.timeline_df = self.timeline_df[valid_intervals]

            after_interval_check = len(self.timeline_df)
            if before_interval_check != after_interval_check:
                logger.info(
                    f"Removed {before_interval_check - after_interval_check} rows with invalid/zero-duration intervals"
                )

            final_count = len(self.timeline_df)

            if initial_count != final_count:
                logger.info(
                    f"Total removed: {initial_count - final_count} rows (missing battery_id + invalid intervals)"
                )

        # Clean battery type data
        if self.battery_type_df is not None:
            self.battery_type_df["battery_id"] = self.battery_type_df[
                "battery_id"
            ].astype(str)

            self.battery_type_df = self.battery_type_df[
                ~self.battery_type_df["battery_id"].str.contains(
                    "kein Tausch", case=False, na=False
                )
            ]

            self.battery_type_df = self.battery_type_df[
                ["battery_id", "battery_type"]
            ].copy()

            # Remove duplicates
            initial_count = len(self.battery_type_df)
            self.battery_type_df = self.battery_type_df.drop_duplicates(
                subset=["battery_id"], keep="first"
            )
            final_count = len(self.battery_type_df)

            if initial_count != final_count:
                logger.info(
                    f"Removed {initial_count - final_count} duplicate battery_id entries"
                )

        logger.info(
            f"After cleaning: {len(self.timeline_df)} timeline records, {len(self.battery_type_df)} battery type records"
        )

    def _build_battery_timelines(self) -> None:
        """Pre-group and sort timelines by battery_id for fast lookup by both reports."""
        logger.info("Pre-indexing battery timelines for fast lookup...")
        if self.timeline_df is None:
            return

        for battery_id, group in self.timeline_df.groupby("battery_id"):
            # Sort by interval_start for each battery (ensures chronological order)
            sorted_group = group.sort_values("interval_start").copy()
            # Convert dates to datetime here if not already (for consistency)
            sorted_group["interval_start"] = pd.to_datetime(
                sorted_group["interval_start"], errors="coerce"
            )
            sorted_group["interval_end"] = pd.to_datetime(
                sorted_group["interval_end"], errors="coerce"
            )
            self.battery_timelines[battery_id] = sorted_group

        logger.info(
            f"Built timelines for {len(self.battery_timelines)} unique batteries"
        )

    # ─────────────── process / validate / write ──────────────────

    def _calculate_battery_age(self, battery_id: str) -> dict:
        """
        Calculate battery age based on timeline intervals.

        Args:
            battery_id: The battery ID to calculate age for

        Returns:
            dict: Contains battery_age, confidence, is_active, vin, note, lifecycle_start, lifecycle_end
        """
        # Default return values
        result = {
            "battery_age": "",
            "confidence": "",
            "is_active": False,
            "source_event_ids": [],
            "vin": "",
            "note": "",
            "lifecycle_start": "",
            "lifecycle_end": "",
        }

        # Get battery timeline
        if battery_id not in self.battery_timelines:
            result["note"] = "No timeline data found"
            return result

        timeline = self.battery_timelines[battery_id]

        # Sort chronologically by interval_start
        timeline = timeline.sort_values("interval_start", na_position="last")

        # Convert interval_start and interval_end to datetime for processing
        timeline = timeline.copy()
        timeline["interval_start"] = pd.to_datetime(
            timeline["interval_start"], errors="coerce"
        )
        timeline["interval_end"] = pd.to_datetime(
            timeline["interval_end"], errors="coerce"
        )

        initial_interval = timeline.iloc[0] if len(timeline) > 0 else None
        last_interval = timeline.iloc[-1] if len(timeline) > 0 else None

        # Determine if battery is active (end date equals today)
        is_active = last_interval is not None and last_interval[
            "interval_end"
        ] == pd.Timestamp(self.today)
        result["is_active"] = is_active
        result["vin"] = last_interval["vin"] if pd.notna(last_interval["vin"]) else ""

        # Set lifecycle start and end dates
        if initial_interval is not None and pd.notna(
            initial_interval["interval_start"]
        ):
            result["lifecycle_start"] = initial_interval["interval_start"].date()

        if last_interval is not None and pd.notna(last_interval["interval_end"]):
            if last_interval["interval_end"] == pd.Timestamp(self.today):
                result["lifecycle_end"] = None
            else:
                result["lifecycle_end"] = last_interval["interval_end"].date()

        # Calculate age using start and end dates (both should be available now)
        start_date = initial_interval["interval_start"]
        end_date = last_interval["interval_end"]

        if pd.notna(start_date) and pd.notna(end_date):
            age_days = (end_date - start_date).days
            result["battery_age"] = age_days

            # Calculate confidence (average of first and last interval confidence)
            first_confidence = (
                initial_interval["confidence"]
                if pd.notna(initial_interval["confidence"])
                else 0.0
            )
            last_confidence = (
                last_interval["confidence"]
                if pd.notna(last_interval["confidence"])
                else 0.0
            )

            result["confidence"] = round((first_confidence + last_confidence) / 2, 2)
        else:
            result["note"] = "Missing start or end date"

        result["source_event_ids"] = last_interval[
            "source_event_ids"
        ]  # used for conflict resolution
        return result

    def process_battery_age_report(self):
        """Process the data to create accure.csv output (battery age summary report)."""
        logger.info("Processing data for battery age summary report (accure.csv)...")

        if self.battery_type_df is None or not self.battery_timelines:
            logger.warning("No data to process")
            return

        # Get unique batteries from pre-grouped timeline data (sorted for consistency)
        unique_batteries = sorted(self.battery_timelines.keys())
        self.stats["total_batteries"] = len(unique_batteries)

        logger.info(f"Processing {len(unique_batteries)} unique batteries")

        # Create battery type lookup dictionary
        battery_type_lookup = {}
        if self.battery_type_df is not None:
            battery_type_lookup = dict(
                zip(
                    self.battery_type_df["battery_id"],
                    self.battery_type_df["battery_type"],
                )
            )

        for battery_id in unique_batteries:
            battery_type = battery_type_lookup.get(battery_id, "")

            if battery_type:
                self.stats["batteries_with_type"] += 1
            else:
                self.stats["batteries_without_type"] += 1

            # Calculate battery age and get additional info
            age_info = self._calculate_battery_age(battery_id)

            # Create record for accure.csv
            accure_record = {
                "battery_id": battery_id,
                "battery_type": battery_type,
                "vin": age_info["vin"],
                "battery_age": age_info["battery_age"],
                "confidence": age_info["confidence"],
                "is_active": age_info["is_active"],
                "source_event_ids": age_info["source_event_ids"],
                "note": age_info["note"],
                "lifecycle_start": age_info["lifecycle_start"],
                "lifecycle_end": age_info["lifecycle_end"],
            }

            self.accure_data.append(accure_record)

        logger.info(f"Processed {len(self.accure_data)} battery records")
        logger.info(f"Batteries with type: {self.stats['batteries_with_type']}")
        logger.info(f"Batteries without type: {self.stats['batteries_without_type']}")

    def process_detailed_lifecycle_report(self):
        """Process detailed lifecycle report with per-interval data."""
        logger.info("Processing detailed lifecycle report...")

        if not self.battery_timelines:
            logger.warning("No timeline data to process")
            return

        # Process battery-by-battery for full lifecycle handling
        unique_batteries = sorted(self.battery_timelines.keys())

        for battery_id in unique_batteries:
            timeline = self.battery_timelines[battery_id]

            # Process each interval in the battery's timeline
            for _, row in timeline.iterrows():
                vin = row["vin"]
                start = row["interval_start"]
                end = row["interval_end"]

                # Calculate duration in days
                duration = ""
                if pd.notna(start) and pd.notna(end):
                    duration = (end - start).days

                # Calculate km_stand for this interval
                km_stand = self._calculate_km_stand(vin, start, end)
                if km_stand != "" and km_stand > 0:
                    self.stats["intervals_with_km_stand"] += 1

                # Create detailed record
                detailed_record = {
                    "battery_id": battery_id,
                    "vin": vin,
                    "interval_start": start.date() if pd.notna(start) else "",
                    "interval_end": end.date() if pd.notna(end) else "",
                    "duration": duration,
                    "km_stand": km_stand,
                    "confidence": row.get("confidence", ""),
                    "source_event_ids": row.get("source_event_ids", ""),
                }

                self.detailed_data.append(detailed_record)
                self.stats["total_intervals"] += 1

        logger.info(
            f"Processed {self.stats['total_intervals']} intervals for detailed report"
        )
        logger.info(
            f"Intervals with km_stand data: {self.stats['intervals_with_km_stand']}"
        )

    def _calculate_km_stand(
        self, vin: str, start_date: pd.Timestamp, end_date: pd.Timestamp
    ) -> float | str:
        """Calculate km_stand (odometer difference) for a given VIN and date range."""
        # Return empty if invalid inputs
        if pd.isna(start_date) or pd.isna(end_date) or start_date >= end_date:
            return ""

        # Get vehicle_id from VIN
        vehicle_id = self.vin_to_vehicle_id.get(vin)
        if not vehicle_id:
            return ""

        # Get daily stats for this vehicle
        if vehicle_id not in self.daily_stats_by_vehicle:
            return ""

        daily_df = self.daily_stats_by_vehicle[vehicle_id].copy()
        if daily_df.empty:
            return ""

        # Ensure date column is datetime and sort
        daily_df["date"] = pd.to_datetime(daily_df["date"], errors="coerce")
        daily_df = daily_df.sort_values(["date", "timestamp_start"])

        # Find effective start km
        start_km = None
        current_date = start_date.date()
        max_search_days = 30  # Limit search to avoid infinite loops
        search_days = 0

        while (
            current_date <= end_date.date()
            and start_km is None
            and search_days < max_search_days
        ):
            day_data = daily_df[daily_df["date"].dt.date == current_date]
            if not day_data.empty:
                # Take the minimum km_start for this date
                start_km = day_data["km_start"].min()
                break
            current_date = pd.Timestamp(current_date) + pd.Timedelta(days=1)
            current_date = current_date.date()
            search_days += 1

        # Find effective end km
        end_km = None
        current_date = end_date.date()
        search_days = 0

        while (
            current_date >= start_date.date()
            and end_km is None
            and search_days < max_search_days
        ):
            day_data = daily_df[daily_df["date"].dt.date == current_date]
            if not day_data.empty:
                # Take the maximum km_end for this date
                end_km = day_data["km_end"].max()
                break
            current_date = pd.Timestamp(current_date) - pd.Timedelta(days=1)
            current_date = current_date.date()
            search_days += 1

        # Calculate difference
        if start_km is not None and end_km is not None and end_km >= start_km:
            return float(end_km - start_km)

        # Log error for debugging
        if start_km is not None and end_km is not None and end_km < start_km:
            error_msg = (
                f"Negative km_stand for VIN {vin}: start_km={start_km}, end_km={end_km}"
            )
            self.stats["errors"].append(error_msg)
            return 0.0

        return ""

    def generate_outputs(self):
        """Generate both the battery age summary report and detailed lifecycle report."""
        logger.info("Generating output files...")

        # Build enhanced stats with processing metadata
        enhanced_stats = self._build_enhanced_stats()

        # Generate battery age summary report (accure.csv)
        age_csv_file, age_stats_file = None, None
        if self.accure_data:
            logger.info("Generating battery age summary report (accure.csv)...")
            age_bundle = OutputBundle(
                records=self.accure_data,
                stats=enhanced_stats,
                csv_path=Path(self.out_path),
                stats_path=Path(self.stats_path),
            )

            writer = LocalCSVWriter()
            output_generator = OutputGenerator(writer)
            age_csv_file, age_stats_file = output_generator(age_bundle)

            logger.info(
                f"Saved {len(self.accure_data)} battery age records to {age_csv_file}"
            )
        else:
            logger.warning("No battery age data to output")

        # Generate detailed lifecycle report
        detailed_csv_file, detailed_stats_file = None, None
        if self.detailed_data:
            logger.info("Generating detailed lifecycle report...")
            detailed_bundle = OutputBundle(
                records=self.detailed_data,
                stats=enhanced_stats,
                csv_path=Path(self.detailed_path),
                stats_path=Path(self.detailed_stats_path),
            )

            writer = LocalCSVWriter()
            output_generator = OutputGenerator(writer)
            detailed_csv_file, detailed_stats_file = output_generator(detailed_bundle)

            logger.info(
                f"Saved {len(self.detailed_data)} detailed interval records to {detailed_csv_file}"
            )
        else:
            logger.warning("No detailed lifecycle data to output")

        logger.info(f"Statistics saved to {age_stats_file or detailed_stats_file}")

        return (
            str(age_csv_file) if age_csv_file else None,
            str(detailed_csv_file) if detailed_csv_file else None,
            (
                str(age_stats_file or detailed_stats_file)
                if (age_stats_file or detailed_stats_file)
                else None
            ),
        )

    def _build_enhanced_stats(self):
        """Build enhanced statistics for analyzer output."""
        enhanced_stats = self.stats.copy()
        enhanced_stats["processing_date"] = str(datetime.now())
        enhanced_stats["battery_age_output_file"] = str(self.out_path)
        enhanced_stats["detailed_lifecycle_output_file"] = str(self.detailed_path)
        enhanced_stats["total_battery_age_records"] = len(self.accure_data)
        enhanced_stats["total_detailed_records"] = len(self.detailed_data)

        return enhanced_stats

    def validate_and_resolve_conflicts(self):
        """Validate that each VIN has at most 2 active batteries (master/slave configuration)."""
        logger.info("Validating result...")
        if not self.accure_data:
            logger.warning("No data to validate")
            return

        # Group by VIN using native Python
        vin_groups = {}
        for i, record in enumerate(self.accure_data):
            vin = record["vin"]
            if vin not in vin_groups:
                vin_groups[vin] = []
            vin_groups[vin].append((i, record))

        for vin, records in vin_groups.items():
            active_records = [
                (i, record) for i, record in records if record["is_active"]
            ]

            if len(active_records) > 2:  # Allow 2 concurrent batteries (master/slave)
                logger.warning(
                    f"VIN {vin} has {len(active_records)} active batteries (expected ≤2)"
                )
                # Log the VIN and the active batteries for debugging, each on a new line
                logger.warning(f"  → Active batteries for VIN {vin}:")
                for i, record in active_records:
                    logger.warning(
                        f"    → Battery {record['battery_id']} with source_event_ids {record['source_event_ids']} and confidence {record['confidence']}"
                    )
                self.stats["errors"].append(f"VIN {vin} has multiple active batteries")

                # Sort by source_event_ids descending and keep top 2
                active_records_sorted = sorted(
                    active_records, key=lambda x: x[1]["source_event_ids"], reverse=True
                )

                # Keep top 2, deactivate the rest
                batteries_to_keep = set()
                for i in range(min(2, len(active_records_sorted))):
                    batteries_to_keep.add(
                        active_records_sorted[i][1]["source_event_ids"]
                    )

                # Deactivate all except the top 2
                for i, record in active_records:
                    if record["source_event_ids"] not in batteries_to_keep:
                        self.accure_data[i]["is_active"] = False
                        self.accure_data[i][
                            "note"
                        ] = "Deactivated due to conflict resolution - kept top 2 source_event_ids"

                kept_ids = [str(id) for id in sorted(batteries_to_keep, reverse=True)]
                logger.info(
                    f"  → Resolved: Kept batteries with source_event_ids {kept_ids} active for VIN {vin}"
                )

    def run(self) -> tuple[str | None, str | None, str | None]:
        """Run the complete analysis pipeline for both reports."""
        self.load()
        self.clean()
        self._build_battery_timelines()
        self.process_battery_age_report()
        self.process_detailed_lifecycle_report()
        self.validate_and_resolve_conflicts()  # Only applies to battery age report
        age_csv, detailed_csv, stats_file = self.generate_outputs()
        return age_csv, detailed_csv, stats_file
